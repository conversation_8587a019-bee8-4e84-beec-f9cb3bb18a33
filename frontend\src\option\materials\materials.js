export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键，自增",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "资料名称",
      prop: "title",
      type: "input",
      search: true,
    },
    {
      label: "资料类型",
      prop: "type",
      type: 'select',
      dicUrl: '/blade-system/dict-biz/dictionary?code=hy_material_type',
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
      search: true,
    },
    {
      label: "文件地址",
      prop: "fileUrl",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      tableDisplay: false,
    },
    {
      label: "文件上传",
      prop: "fileUpload",
      type: "upload",
      listType: "text",
      dataType: "string",
      multiple: false,
      hide: true,
      limit: 1,
      action: "/blade-resource/oss/endpoint/put-file",
      propsHttp: {
        res: "data",
        url: "link",
      },
      tip: "请上传PDF格式文件，文件大小不超过50MB",
      accept: ".pdf",
      span: 24,
      tableDisplay: false,
    },
    {
      label: "上传时间",
      prop: "uploadTime",
      type: "input",
    },
    {
      label: "上传人",
      prop: "uploader",
      type: "input",
    },
    {
      label: "描述",
      prop: "description",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
